// auto generated constant for packages/etf-comparator-recommend-list/src/components/RecommendCard.tsx
// 加入对比按钮文案
export const COMPARE_BUTTON_TEXT = "加入对比";
// PK标签文案
export const PK_BADGE_TEXT = "PK";
// 展开按钮文案
export const EXPAND_BUTTON_TEXT = "展开";
// 收起按钮文案
export const COLLAPSE_BUTTON_TEXT = "收起";
// ETF按钮宽度 - 两列布局
export const ETF_BUTTON_WIDTH_TWO_COLUMN = "w-40";
// ETF按钮宽度 - 三列布局
export const ETF_BUTTON_WIDTH_THREE_COLUMN = "w-24";
// ETF按钮间距
export const ETF_BUTTON_GAP_SPACING = "gap-x-[5px]";

// auto generated constant for packages/etf-comparator-recommend-list/src/index.tsx
// 默认推荐列表标题
export const DEFAULT_RECOMMEND_TITLE = "热门推荐";
// 推荐卡片顶部间距（像素）
export const RECOMMEND_CARD_TOP_SPACING = 48;
// 推荐卡片垂直间距（像素）
export const RECOMMEND_CARD_VERTICAL_SPACING = 159;

// auto generated constant for packages/etf-comparator-recommend-list/src/index.stories.tsx
// Storybook 示例类别
export const STORY_CATEGORY_EXAMPLE = "Example";
// 桌面端视口配置
export const VIEWPORT_DESKTOP = "desktop";
// 浅色背景主题
export const BACKGROUND_LIGHT = "light";
// 设计系统文档链接
export const DESIGN_SYSTEM_URL =
  "https://design-system.com/components/etf-list";
// Storybook 文本控件类型
export const CONTROL_TEXT = "text";
// Storybook 对象控件类型
export const CONTROL_OBJECT = "object";
// ETF 点击动作标识
export const ACTION_ETF_CLICKED = "etf-clicked";
// 加入对比动作标识
export const ACTION_COMPARE_CLICKED = "compare-clicked";
