/**
 * useTextExpand.ts
 * 文本展开/收起功能的自定义Hook
 *
 * 功能特性:
 * 1. 自动检测文本是否超过指定行数
 * 2. 提供展开/收起状态管理
 * 3. 返回必要的ref、状态和处理函数
 */

import { useState, useEffect, useRef } from "react";

export interface UseTextExpandOptions {
  text: string; // 要检测的文本内容
  maxLines?: number; // 最大显示行数，默认为3
}

export interface UseTextExpandReturn {
  textContainerRef: React.RefObject<HTMLDivElement>; // 文本容器的ref
  isExpanded: boolean; // 是否已展开
  showExpandButton: boolean; // 是否显示展开按钮
  handleToggleExpand: () => void; // 切换展开/收起的处理函数
}

/**
 * 文本展开/收起功能的自定义Hook
 * @param options 配置选项
 * @returns 返回文本展开功能所需的状态和方法
 */
export const useTextExpand = ({
  text,
  maxLines = 3,
}: UseTextExpandOptions): UseTextExpandReturn => {
  // 展开/收起状态管理
  const [isExpanded, setIsExpanded] = useState(false);
  const [showExpandButton, setShowExpandButton] = useState(false);

  // 文本容器引用
  const textContainerRef = useRef<HTMLDivElement>(null);

  // 检测文本是否超过指定行数
  useEffect(() => {
    const checkTextOverflow = () => {
      if (textContainerRef.current) {
        const element = textContainerRef.current;
        // 检查scrollHeight是否大于clientHeight，表示内容被截断
        setShowExpandButton(element.scrollHeight > element.clientHeight);
      }
    };

    // 使用setTimeout确保DOM已经渲染完成
    const timer = setTimeout(checkTextOverflow, 0);

    return () => clearTimeout(timer);
    // 当text改变时重新检测
  }, [text, maxLines]);

  // 处理展开/收起切换
  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return {
    textContainerRef,
    isExpanded,
    showExpandButton,
    handleToggleExpand,
  };
};
