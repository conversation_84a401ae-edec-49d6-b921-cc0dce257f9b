/**
 * 文件: index.stories.tsx
 * 描述: ETF推荐列表的Storybook展示
 * 作者: 同花顺代码生成团队
 *
 * 功能特性:
 * 1. 展示不同状态的ETF推荐列表
 * 2. 支持交互功能
 * 3. 包含完整的Meta配置
 * 4. 提供详细的参数说明和控制面板
 */

import {
  BACKGROUND_LIGHT,
  CONTROL_OBJECT,
  ACTION_COMPARE_CLICKED,
  STORY_CATEGORY_EXAMPLE,
  VIEWPORT_DESKTOP,
  DESIGN_SYSTEM_URL,
  ACTION_ETF_CLICKED,
  CONTROL_TEXT,
} from "./constants/index.ts";

import type { Meta, StoryObj } from "@storybook/react";
import { EtfComparatorRecommendList } from "./index";

// 初始化meta - ETF推荐列表组件
const meta: Meta<typeof EtfComparatorRecommendList> = {
  title: "Example/EtfComparatorRecommendList",
  component: EtfComparatorRecommendList,
  tags: ["autodocs"],
  parameters: {
    docs: {
      description: {
        component:
          "ETF推荐列表组件，支持多种状态展示和交互功能，包含推荐主题信息、涨跌幅、描述文案以及相关ETF产品按钮。支持ETF点击跳转和加入对比功能。",
      },
    },
    viewport: {
      defaultViewport: VIEWPORT_DESKTOP,
    },
    backgrounds: {
      default: BACKGROUND_LIGHT,
    },
    chromatic: {
      pauseAnimationAtEnd: true,
    },
    customMetadata: {
      designUrl: DESIGN_SYSTEM_URL,
    },
  },
  argTypes: {
    title: {
      description: "推荐列表标题",
      control: CONTROL_TEXT,
    },
    recommendList: {
      description: "推荐列表数据，包含主题名称、涨跌幅、描述文案和ETF列表",
      control: CONTROL_OBJECT,
    },
    onEtfClick: {
      description: "ETF点击回调函数",
      action: ACTION_ETF_CLICKED,
    },
    onCompareClick: {
      description: "加入对比点击回调函数",
      action: ACTION_COMPARE_CLICKED,
    },
  },
};

export default meta;
type Story = StoryObj<typeof EtfComparatorRecommendList>;

// Default Story: 展示默认的推荐列表状态，包含多个推荐项目
export const Default: Story = {
  name: "Default",
  parameters: {
    docs: {
      description: {
        story:
          "展示默认的推荐列表状态，包含多个推荐项目，每个项目包含主题名称、涨跌幅、描述文案和相关ETF产品按钮。",
      },
    },
  },
  args: {
    title: "热门推荐",
    recommendList: [
      {
        title: "新能源汽车主题",
        changeRate: "+8.52%",
        description:
          "新能源汽车行业快速发展，产业链相关标的表现亮眼，政策持续利好，市场前景广阔，投资价值显著。",
        etfList: [{ name: "新能源汽车ETF" }, { name: "电动车ETF" }],
      },
      {
        title: "超长测试-人工智能主题人工智能主题人工智能主题",
        changeRate: "-2.31%",
        description:
          "超长测试-AI技术革命推动产业升级，相关概念股受到市场关注，长期发展空间巨大，短期存在调整压力。AI技术革命推动产业升级，相关概念股受到市场关注，长期发展空间巨大，短期存在调整压力。",
        etfList: [{ name: "AI智能ETF" }, { name: "科技创新ETF" }],
      },
      {
        title: "医药健康主题",
        changeRate: "+5.73%",
        description:
          "医药行业受益于人口老龄化趋势，创新药研发加速，医疗器械需求增长，板块整体向好。",
        etfList: [{ name: "医药ETF" }, { name: "生物医药ETF" }],
      },
    ],
    onEtfClick: (etfName: string) => {
      console.log(`点击了ETF: ${etfName}`);
    },
    onCompareClick: () => {
      console.log("点击了加入对比按钮");
    },
  },
};

// Single Item Story: 展示只有一个推荐项目的状态
export const SingleItem: Story = {
  name: "SingleItem",
  parameters: {
    docs: {
      description: {
        story: "展示只有一个推荐项目的状态，适用于精选推荐或特殊场景展示。",
      },
    },
  },
  args: {
    title: "精选推荐",
    recommendList: [
      {
        title: "半导体芯片主题",
        changeRate: "+12.68%",
        description:
          "半导体行业景气度回升，国产替代加速推进，相关标的估值修复明显，投资机会凸显，建议重点关注。",
        etfList: [{ name: "芯片ETF" }, { name: "半导体ETF" }],
      },
    ],
    onEtfClick: (etfName: string) => {
      console.log(`点击了ETF: ${etfName}`);
    },
    onCompareClick: () => {
      console.log("点击了加入对比按钮");
    },
  },
};

// Multiple ETFs Story: 展示包含不同数量ETF的推荐项目
export const MultipleETFs: Story = {
  name: "MultipleETFs",
  parameters: {
    docs: {
      description: {
        story:
          "展示包含不同数量ETF的推荐项目，验证组件对不同ETF数量的布局适配能力。",
      },
    },
  },
  args: {
    title: "主题多样化",
    recommendList: [
      {
        title: "消费升级主题",
        changeRate: "+3.42%",
        description:
          "消费升级趋势明确，高端消费品牌受益，相关ETF产品丰富，为投资者提供多样化选择。",
        etfList: [
          { name: "消费ETF" },
          { name: "白酒ETF" },
          { name: "食品饮料ETF" },
        ],
      },
      {
        title: "金融科技主题",
        changeRate: "-1.25%",
        description: "金融科技创新持续，数字化转型加速推进。",
        etfList: [{ name: "金融ETF" }],
      },
    ],
    onEtfClick: (etfName: string) => {
      console.log(`点击了ETF: ${etfName}`);
    },
    onCompareClick: () => {
      console.log("点击了加入对比按钮");
    },
  },
};

// Interactive Story: 展示交互功能，包含点击回调的完整示例
export const WithCallbacks: Story = {
  name: "WithCallbacks",
  parameters: {
    docs: {
      description: {
        story:
          "展示完整的交互功能，包含ETF点击跳转和加入对比功能的回调处理，用于测试组件的交互响应。",
      },
    },
  },
  args: {
    title: "交互演示",
    recommendList: [
      {
        title: "绿色能源主题",
        changeRate: "+6.89%",
        description:
          "绿色能源政策持续加码，清洁能源需求增长，相关产业链投资价值凸显，点击体验交互功能。",
        etfList: [{ name: "清洁能源ETF" }, { name: "光伏ETF" }],
      },
      {
        title: "数字经济主题",
        changeRate: "+4.15%",
        description:
          "数字经济蓬勃发展，云计算、大数据等细分领域表现突出，相关ETF产品值得关注。",
        etfList: [
          { name: "互联网ETF" },
          { name: "云计算ETF" },
          { name: "5G通信ETF" },
        ],
      },
    ],
    onEtfClick: (etfName: string) => {
      alert(`您点击了：${etfName}，即将跳转至ETF详情页`);
    },
    onCompareClick: () => {
      alert("已加入对比列表，可在对比页面查看详细对比信息");
    },
  },
};
